// T3Config.h
#pragma once

#include <string>

namespace T3Config {
    // t3网络验证配置
    static const std::string Host = "http://w.t3yanzheng.com"; // t3网络验证线路地址
    static const std::string AppKey = "561B6E41572C899261D7E73FBF347EE3"; // t3网络验证程序应用密钥
    static const std::string Base64key = "wr1juNVXcvBWF+DkaKxpnG2UygE7TRQOfqZ9YSAIohb6CeH3J/mdPM85zi0Llst4"; // t3网络验证程序Base64自定义编码集

    // API路径
    static const std::string Path_SingleLogin = "777028A1B8094CFF"; // t3网络验证程序单码卡密登录API调用路径
    static const std::string Path_IsSingleLoginStatus = "573F8FBBD386E672"; // t3网络验证程序单码卡密登录状态查询API调用路径
    static const std::string Path_GetProgramNotice = "97D15E87CF662CC5"; // t3网络验证程序获取公告API调用路径
    static const std::string Path_GetProgramVersionNumber = "CBC8373B9A88C85C"; // t3网络验证程序获取版本号API调用路径
    static const std::string Path_GetValueContent = "FEE8B8AB889A75D2"; // t3网络验证程序获取变量内容API调用路径
}