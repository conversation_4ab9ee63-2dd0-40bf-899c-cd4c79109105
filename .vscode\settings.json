{"files.associations": {"xstring": "cpp", "string": "cpp", "iostream": "cpp", "ostream": "cpp", "algorithm": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "deque": "cpp", "exception": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "map": "cpp", "memory": "cpp", "new": "cpp", "ratio": "cpp", "set": "cpp", "sstream": "cpp", "stack": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "utility": "cpp", "vector": "cpp", "xfacet": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocinfo": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xstddef": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp"}}